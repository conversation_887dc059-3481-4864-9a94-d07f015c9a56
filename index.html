<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - Product Manager & Entrepreneur</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        accent: '#ec4899',
                        'gradient-start': '#667eea',
                        'gradient-end': '#764ba2',
                        'neon-blue': '#00f5ff',
                        'neon-purple': '#bf00ff',
                        'neon-pink': '#ff0080',
                        'dark-bg': '#0a0a0f',
                        'card-bg': '#1a1a2e'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', '-apple-system', 'sans-serif'],
                        'display': ['Space Grotesk', 'Inter', 'sans-serif'],
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s infinite',
                        'bounce-slow': 'bounce 3s infinite',
                        'gradient': 'gradient 8s linear infinite',
                        'fadeInUp': 'fadeInUp 0.8s ease-out forwards',
                        'slideInLeft': 'slideInLeft 0.8s ease-out forwards',
                        'slideInRight': 'slideInRight 0.8s ease-out forwards',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'rotate-slow': 'rotate 20s linear infinite',
                        'scale-pulse': 'scale-pulse 3s ease-in-out infinite',
                        'text-shimmer': 'text-shimmer 2.5s ease-in-out infinite',
                        'border-flow': 'border-flow 3s linear infinite',
                        'particle-float': 'particle-float 8s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        },
                        gradient: {
                            '0%, 100%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' }
                        },
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-50px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        slideInRight: {
                            '0%': { opacity: '0', transform: 'translateX(50px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(99, 102, 241, 0.5)' },
                            '100%': { boxShadow: '0 0 40px rgba(99, 102, 241, 0.8), 0 0 60px rgba(139, 92, 246, 0.3)' }
                        },
                        'scale-pulse': {
                            '0%, 100%': { transform: 'scale(1)' },
                            '50%': { transform: 'scale(1.05)' }
                        },
                        'text-shimmer': {
                            '0%': { backgroundPosition: '-200% center' },
                            '100%': { backgroundPosition: '200% center' }
                        },
                        'border-flow': {
                            '0%': { backgroundPosition: '0% 50%' },
                            '100%': { backgroundPosition: '200% 50%' }
                        },
                        'particle-float': {
                            '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                            '33%': { transform: 'translateY(-30px) rotate(120deg)' },
                            '66%': { transform: 'translateY(15px) rotate(240deg)' }
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Enhanced Glass Morphism */
        .glass-morphism {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .glass-morphism-strong {
            background: rgba(26, 26, 46, 0.7);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(99, 102, 241, 0.2);
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.1);
        }

        /* Enhanced Gradient Backgrounds */
        .gradient-bg {
            background: linear-gradient(-45deg, #0a0a0f, #1a1a2e, #16213e, #0f3460);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
            position: relative;
        }

        .gradient-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(-45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.1), rgba(59, 130, 246, 0.1));
            background-size: 400% 400%;
            animation: gradient 20s ease infinite reverse;
        }

        /* Enhanced Text Gradients */
        .text-gradient {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: text-shimmer 3s ease-in-out infinite;
        }

        .text-shimmer {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            background-size: 200% 100%;
            -webkit-background-clip: text;
            background-clip: text;
            animation: text-shimmer 2s infinite;
        }

        /* Enhanced Card Hover Effects */
        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .card-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
            transition: left 0.5s;
        }

        .card-hover:hover::before {
            left: 100%;
        }

        .card-hover:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 30px 60px rgba(99, 102, 241, 0.2), 0 0 0 1px rgba(99, 102, 241, 0.1);
        }

        /* Enhanced Blob Animation */
        .blob {
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation: blob 20s infinite;
            filter: blur(1px);
        }

        @keyframes blob {
            0%, 100% {
                border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
                transform: rotate(0deg) scale(1);
            }
            25% {
                border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%;
                transform: rotate(90deg) scale(1.1);
            }
            50% {
                border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%;
                transform: rotate(180deg) scale(0.9);
            }
            75% {
                border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%;
                transform: rotate(270deg) scale(1.05);
            }
        }

        /* Enhanced Dialog Effects */
        .dialog-backdrop {
            backdrop-filter: blur(12px);
            background: rgba(0, 0, 0, 0.6);
        }

        .dialog-content {
            animation: slideInUp 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        @keyframes slideInUp {
            from {
                transform: translateY(100px) scale(0.9);
                opacity: 0;
            }
            to {
                transform: translateY(0) scale(1);
                opacity: 1;
            }
        }

        .stacked-dialog {
            position: relative;
            transform-style: preserve-3d;
        }

        .dialog-stack {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            transform-origin: center;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .dialog-stack:nth-child(1) { transform: translateZ(0px) scale(1); z-index: 3; }
        .dialog-stack:nth-child(2) { transform: translateZ(-20px) scale(0.95); z-index: 2; }
        .dialog-stack:nth-child(3) { transform: translateZ(-40px) scale(0.9); z-index: 1; }

        /* Enhanced Scroll Animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .animate-on-scroll.animate {
            opacity: 1;
            transform: translateY(0);
        }

        /* Neon Effects */
        .neon-border {
            position: relative;
            border: 2px solid transparent;
            background: linear-gradient(45deg, #6366f1, #8b5cf6, #ec4899) border-box;
            background-clip: padding-box;
        }

        .neon-border::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #6366f1, #8b5cf6, #ec4899, #6366f1);
            background-size: 200% 200%;
            border-radius: inherit;
            z-index: -1;
            animation: border-flow 3s linear infinite;
        }

        /* Particle Effects */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #6366f1, transparent);
            border-radius: 50%;
            animation: particle-float 8s ease-in-out infinite;
        }

        .particle:nth-child(2) { animation-delay: -2s; background: radial-gradient(circle, #8b5cf6, transparent); }
        .particle:nth-child(3) { animation-delay: -4s; background: radial-gradient(circle, #ec4899, transparent); }
        .particle:nth-child(4) { animation-delay: -6s; background: radial-gradient(circle, #06b6d4, transparent); }

        /* Enhanced Typography */
        .hero-text {
            font-family: 'Space Grotesk', sans-serif;
            font-weight: 700;
            letter-spacing: -0.02em;
            line-height: 0.9;
        }

        .section-title {
            font-family: 'Space Grotesk', sans-serif;
            font-weight: 600;
            letter-spacing: -0.01em;
        }

        /* Glow Effects */
        .glow-on-hover {
            transition: all 0.3s ease;
        }

        .glow-on-hover:hover {
            box-shadow: 0 0 30px rgba(99, 102, 241, 0.6), 0 0 60px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #0a0a0f;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #8b5cf6, #ec4899);
        }
    </style>
</head>
<body class="bg-dark-bg text-white overflow-x-hidden">
    <!-- Particle Background -->
    <div class="fixed inset-0 pointer-events-none z-0">
        <div class="particle" style="top: 20%; left: 10%;"></div>
        <div class="particle" style="top: 60%; left: 80%;"></div>
        <div class="particle" style="top: 30%; left: 70%;"></div>
        <div class="particle" style="top: 80%; left: 20%;"></div>
        <div class="particle" style="top: 10%; left: 50%;"></div>
        <div class="particle" style="top: 70%; left: 30%;"></div>
    </div>
    <!-- Enhanced Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-morphism-strong">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <div class="flex items-center">
                    <div class="text-3xl font-bold text-gradient hero-text">Saura Odalvi</div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#home" class="relative px-4 py-2 rounded-full hover:text-primary transition-all duration-300 group">
                            <span class="relative z-10">Home</span>
                            <div class="absolute inset-0 bg-primary/10 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                        </a>
                        <a href="#about" class="relative px-4 py-2 rounded-full hover:text-primary transition-all duration-300 group">
                            <span class="relative z-10">About</span>
                            <div class="absolute inset-0 bg-primary/10 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                        </a>
                        <a href="#experience" class="relative px-4 py-2 rounded-full hover:text-primary transition-all duration-300 group">
                            <span class="relative z-10">Experience</span>
                            <div class="absolute inset-0 bg-primary/10 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                        </a>
                        <a href="#projects" class="relative px-4 py-2 rounded-full hover:text-primary transition-all duration-300 group">
                            <span class="relative z-10">Projects</span>
                            <div class="absolute inset-0 bg-primary/10 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                        </a>
                        <a href="#contact" class="relative px-4 py-2 rounded-full hover:text-primary transition-all duration-300 group">
                            <span class="relative z-10">Contact</span>
                            <div class="absolute inset-0 bg-primary/10 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                        </a>
                    </div>
                </div>
                <button class="md:hidden p-2 rounded-lg glass-morphism" onclick="toggleMobileMenu()">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
        <div id="mobileMenu" class="hidden md:hidden glass-morphism-strong">
            <div class="px-4 pt-2 pb-4 space-y-2">
                <a href="#home" class="block px-4 py-3 rounded-lg hover:bg-primary/10 hover:text-primary transition-all">Home</a>
                <a href="#about" class="block px-4 py-3 rounded-lg hover:bg-primary/10 hover:text-primary transition-all">About</a>
                <a href="#experience" class="block px-4 py-3 rounded-lg hover:bg-primary/10 hover:text-primary transition-all">Experience</a>
                <a href="#projects" class="block px-4 py-3 rounded-lg hover:bg-primary/10 hover:text-primary transition-all">Projects</a>
                <a href="#contact" class="block px-4 py-3 rounded-lg hover:bg-primary/10 hover:text-primary transition-all">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Enhanced Hero Section -->
    <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="absolute inset-0 gradient-bg"></div>

        <!-- Enhanced Floating elements -->
        <div class="absolute top-20 left-20 w-40 h-40 bg-gradient-to-r from-primary/30 to-secondary/30 rounded-full blur-2xl animate-float blob"></div>
        <div class="absolute bottom-20 right-20 w-56 h-56 bg-gradient-to-r from-secondary/30 to-accent/30 rounded-full blur-2xl animate-float blob" style="animation-delay: -2s;"></div>
        <div class="absolute top-1/2 left-10 w-32 h-32 bg-gradient-to-r from-accent/30 to-primary/30 rounded-full blur-2xl animate-bounce-slow blob"></div>
        <div class="absolute top-1/3 right-1/4 w-24 h-24 bg-gradient-to-r from-neon-blue/20 to-neon-purple/20 rounded-full blur-xl animate-pulse-slow"></div>

        <div class="relative z-10 text-center px-4 sm:px-6 lg:px-8">
            <div class="animate-fadeInUp">
                <div class="mb-8">
                    <div class="inline-block px-6 py-3 glass-morphism rounded-full mb-6">
                        <span class="text-primary font-semibold">👋 Welcome to my digital space</span>
                    </div>
                </div>
                <h1 class="hero-text text-5xl sm:text-7xl lg:text-9xl font-extrabold mb-8 leading-none">
                    <span class="block text-white mb-4">Hi, I'm</span>
                    <span class="block text-gradient text-shimmer">Saura Odalvi</span>
                </h1>
                <p class="text-xl sm:text-2xl lg:text-4xl mb-12 text-gray-300 max-w-5xl mx-auto leading-relaxed">
                    Product Manager & Entrepreneur crafting the future of
                    <span class="text-gradient font-semibold">digital experiences</span>
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                    <button onclick="openStackedDialog()" class="group relative bg-gradient-to-r from-primary to-secondary hover:from-secondary hover:to-accent text-white px-10 py-5 rounded-full font-bold text-lg transition-all duration-300 transform hover:scale-105 glow-on-hover">
                        <span class="relative z-10 flex items-center">
                            🚀 Explore My Journey
                            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </span>
                    </button>
                    <a href="#contact" class="group relative border-2 border-white/30 text-white hover:border-primary hover:text-primary px-10 py-5 rounded-full font-bold text-lg transition-all duration-300 transform hover:scale-105 glass-morphism">
                        <span class="flex items-center">
                            Let's Connect
                            <svg class="w-5 h-5 ml-2 group-hover:rotate-12 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Scroll indicator -->
        <div class="absolute bottom-12 left-1/2 transform -translate-x-1/2">
            <div class="flex flex-col items-center animate-bounce">
                <span class="text-sm text-gray-400 mb-2">Scroll to explore</span>
                <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
                    <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced About Section -->
    <section id="about" class="py-24 bg-card-bg relative">
        <div class="absolute inset-0 bg-gradient-to-b from-dark-bg via-card-bg to-dark-bg"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-20 animate-on-scroll">
                <div class="inline-block px-6 py-3 glass-morphism rounded-full mb-6">
                    <span class="text-primary font-semibold">🎯 Get to know me</span>
                </div>
                <h2 class="section-title text-5xl lg:text-6xl font-bold text-gradient mb-8">About Me</h2>
                <p class="text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    I'm a visionary product manager and entrepreneur passionate about building products that make a
                    <span class="text-gradient font-semibold">real difference</span> in people's lives.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-16 items-center">
                <div class="animate-on-scroll">
                    <div class="glass-morphism-strong rounded-3xl p-10 card-hover relative overflow-hidden">
                        <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/20 to-transparent rounded-full blur-2xl"></div>
                        <h3 class="section-title text-3xl font-bold mb-8 text-gradient">My Mission</h3>
                        <p class="text-gray-300 mb-8 leading-relaxed text-lg">
                            To bridge the gap between innovative technology and human-centered design, creating products that not only solve complex problems but also delight users at every touchpoint.
                        </p>
                        <div class="grid grid-cols-2 gap-6">
                            <div class="text-center p-4 glass-morphism rounded-2xl">
                                <div class="text-4xl font-bold text-gradient mb-2">5+</div>
                                <div class="text-gray-400 font-medium">Years Experience</div>
                            </div>
                            <div class="text-center p-4 glass-morphism rounded-2xl">
                                <div class="text-4xl font-bold text-gradient mb-2">50+</div>
                                <div class="text-gray-400 font-medium">Projects Delivered</div>
                            </div>
                            <div class="text-center p-4 glass-morphism rounded-2xl">
                                <div class="text-4xl font-bold text-gradient mb-2">10+</div>
                                <div class="text-gray-400 font-medium">Products Launched</div>
                            </div>
                            <div class="text-center p-4 glass-morphism rounded-2xl">
                                <div class="text-4xl font-bold text-gradient mb-2">100K+</div>
                                <div class="text-gray-400 font-medium">Users Impacted</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="animate-on-scroll">
                    <div class="space-y-8">
                        <div class="glass-morphism-strong rounded-3xl p-8 card-hover relative overflow-hidden group">
                            <div class="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div class="relative z-10">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center mr-4">
                                        <span class="text-2xl">🎯</span>
                                    </div>
                                    <h4 class="text-2xl font-bold text-primary">Product Strategy</h4>
                                </div>
                                <p class="text-gray-300 text-lg leading-relaxed">Developing comprehensive product roadmaps that align with business objectives and user needs, ensuring every feature delivers maximum value.</p>
                            </div>
                        </div>
                        <div class="glass-morphism-strong rounded-3xl p-8 card-hover relative overflow-hidden group">
                            <div class="absolute inset-0 bg-gradient-to-r from-secondary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div class="relative z-10">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-to-r from-secondary to-accent rounded-2xl flex items-center justify-center mr-4">
                                        <span class="text-2xl">🚀</span>
                                    </div>
                                    <h4 class="text-2xl font-bold text-secondary">Innovation Leadership</h4>
                                </div>
                                <p class="text-gray-300 text-lg leading-relaxed">Leading cross-functional teams to deliver breakthrough products and features that push the boundaries of what's possible.</p>
                            </div>
                        </div>
                        <div class="glass-morphism-strong rounded-3xl p-8 card-hover relative overflow-hidden group">
                            <div class="absolute inset-0 bg-gradient-to-r from-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div class="relative z-10">
                                <div class="flex items-center mb-4">
                                    <div class="w-12 h-12 bg-gradient-to-r from-accent to-primary rounded-2xl flex items-center justify-center mr-4">
                                        <span class="text-2xl">💡</span>
                                    </div>
                                    <h4 class="text-2xl font-bold text-accent">Entrepreneurial Vision</h4>
                                </div>
                                <p class="text-gray-300 text-lg leading-relaxed">Building startups from concept to scale with a focus on sustainable growth and meaningful impact on society.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Experience Section -->
    <section id="experience" class="py-20 bg-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-4xl lg:text-5xl font-bold text-gradient mb-6">Experience</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    My journey through the world of product management and entrepreneurship
                </p>
            </div>
            
            <div class="relative">
                <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-primary to-secondary"></div>
                
                <div class="space-y-16">
                    <div class="flex flex-col md:flex-row items-center animate-on-scroll">
                        <div class="md:w-1/2 md:pr-8">
                            <div class="glass-morphism rounded-2xl p-8 card-hover">
                                <div class="flex items-center mb-4">
                                    <div class="w-4 h-4 bg-primary rounded-full mr-3"></div>
                                    <span class="text-primary font-semibold">2023 - Present</span>
                                </div>
                                <h3 class="text-2xl font-bold mb-2">Senior Product Manager</h3>
                                <p class="text-gray-400 mb-4">Leading Product Innovation</p>
                                <p class="text-gray-300 leading-relaxed">
                                    Spearheading product strategy for next-generation digital solutions, managing cross-functional teams of 15+ members, and driving product-market fit for emerging technologies.
                                </p>
                            </div>
                        </div>
                        <div class="md:w-1/2 md:pl-8 mt-8 md:mt-0">
                            <div class="relative">
                                <div class="w-full h-64 bg-gradient-to-r from-primary to-secondary rounded-2xl blob opacity-20"></div>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="text-6xl">🎯</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex flex-col md:flex-row-reverse items-center animate-on-scroll">
                        <div class="md:w-1/2 md:pl-8">
                            <div class="glass-morphism rounded-2xl p-8 card-hover">
                                <div class="flex items-center mb-4">
                                    <div class="w-4 h-4 bg-secondary rounded-full mr-3"></div>
                                    <span class="text-secondary font-semibold">2021 - 2023</span>
                                </div>
                                <h3 class="text-2xl font-bold mb-2">Product Manager</h3>
                                <p class="text-gray-400 mb-4">Scaling Digital Products</p>
                                <p class="text-gray-300 leading-relaxed">
                                    Led the development of multiple B2B and B2C products, increased user engagement by 300%, and established data-driven decision making processes across the organization.
                                </p>
                            </div>
                        </div>
                        <div class="md:w-1/2 md:pr-8 mt-8 md:mt-0">
                            <div class="relative">
                                <div class="w-full h-64 bg-gradient-to-r from-secondary to-accent rounded-2xl blob opacity-20"></div>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="text-6xl">🚀</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex flex-col md:flex-row items-center animate-on-scroll">
                        <div class="md:w-1/2 md:pr-8">
                            <div class="glass-morphism rounded-2xl p-8 card-hover">
                                <div class="flex items-center mb-4">
                                    <div class="w-4 h-4 bg-accent rounded-full mr-3"></div>
                                    <span class="text-accent font-semibold">2019 - 2021</span>
                                </div>
                                <h3 class="text-2xl font-bold mb-2">Entrepreneur & Founder</h3>
                                <p class="text-gray-400 mb-4">Building from Ground Up</p>
                                <p class="text-gray-300 leading-relaxed">
                                    Founded and scaled a tech startup from concept to revenue, built a team of 20+ professionals, and successfully raised seed funding while maintaining product-market fit.
                                </p>
                            </div>
                        </div>
                        <div class="md:w-1/2 md:pl-8 mt-8 md:mt-0">
                            <div class="relative">
                                <div class="w-full h-64 bg-gradient-to-r from-accent to-primary rounded-2xl blob opacity-20"></div>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="text-6xl">💡</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-4xl lg:text-5xl font-bold text-gradient mb-6">Featured Projects</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Showcasing the products and solutions I've built that have made a real impact
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass-morphism rounded-2xl p-8 card-hover animate-on-scroll">
                    <div class="text-4xl mb-4">📱</div>
                    <h3 class="text-2xl font-bold mb-4 text-primary">SaaS Platform</h3>
                    <p class="text-gray-300 mb-6">
                        Developed a comprehensive SaaS platform that serves 10,000+ businesses with advanced analytics and automation features.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm">Product Strategy</span>
                        <span class="px-3 py-1 bg-secondary/20 text-secondary rounded-full text-sm">User Research</span>
                        <span class="px-3 py-1 bg-accent/20 text-accent rounded-full text-sm">Analytics</span>
                    </div>
                    <button class="w-full bg-primary hover:bg-primary/80 text-white px-6 py-3 rounded-full font-semibold transition-all transform hover:scale-105">
                        View Case Study
                    </button>
                </div>
                
                <div class="glass-morphism rounded-2xl p-8 card-hover animate-on-scroll">
                    <div class="text-4xl mb-4">🎨</div>
                    <h3 class="text-2xl font-bold mb-4 text-secondary">Design System</h3>
                    <p class="text-gray-300 mb-6">
                        Created a comprehensive design system that improved development velocity by 40% and ensured consistent user experiences.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm">Design Systems</span>
                        <span class="px-3 py-1 bg-secondary/20 text-secondary rounded-full text-sm">UX/UI</span>
                        <span class="px-3 py-1 bg-accent/20 text-accent rounded-full text-sm">Scalability</span>
                    </div>
                    <button class="w-full bg-secondary hover:bg-secondary/80 text-white px-6 py-3 rounded-full font-semibold transition-all transform hover:scale-105">
                        View Case Study
                    </button>
                </div>
                
                <div class="glass-morphism rounded-2xl p-8 card-hover animate-on-scroll">
                    <div class="text-4xl mb-4">🤖</div>
                    <h3 class="text-2xl font-bold mb-4 text-accent">AI-Powered Tool</h3>
                    <p class="text-gray-300 mb-6">
                        Built an AI-powered automation tool that reduced manual work by 60% and increased productivity for thousands of users.
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm">AI/ML</span>
                        <span class="px-3 py-1 bg-secondary/20 text-secondary rounded-full text-sm">Automation</span>
                        <span class="px-3 py-1 bg-accent/20 text-accent rounded-full text-sm">Innovation</span>
                    </div>
                    <button class="w-full bg-accent hover:bg-accent/80 text-white px-6 py-3 rounded-full font-semibold transition-all transform hover:scale-105">
                        View Case Study
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-on-scroll">
                <h2 class="text-4xl lg:text-5xl font-bold text-gradient mb-6">Let's Build Something Amazing</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Ready to collaborate on the next big thing? Let's connect and explore opportunities together.
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-12">
                <div class="animate-on-scroll">
                    <div class="glass-morphism rounded-2xl p-8">
                        <h3 class="text-2xl font-bold mb-6 text-gradient">Get in Touch</h3>
                        <div class="space-y-6">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold">Email</div>
                                    <div class="text-gray-400"><EMAIL></div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-secondary/20 rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold">LinkedIn</div>
                                    <div class="text-gray-400">linkedin.com/in/sauraodalvi</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-accent" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.024-3.639.219-1.225 1.407-5.956 1.407-5.956s-.36-.72-.36-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.655 2.568-.994 3.995-.283 1.194.599 2.169 1.777 2.169 2.133 0 3.772-2.249 3.772-5.495 0-2.873-2.064-4.882-5.012-4.882-3.414 0-5.418 2.561-5.418 5.207 0 1.031.397 2.138.893 2.738.098.119.112.224.083.345l-.333 1.36c-.053.22-.174.267-.402.161-1.499-.698-2.436-2.888-2.436-4.649 0-3.785 2.75-7.262 7.929-7.262 4.163 0 7.398 2.967 7.398 6.931 0 4.136-2.607 7.464-6.227 7.464-1.216 0-2.357-.631-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.001 12.017 24.001c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-semibold">Twitter</div>
                                    <div class="text-gray-400">@sauraodalvi</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="animate-on-scroll">
                    <div class="glass-morphism rounded-2xl p-8">
                        <h3 class="text-2xl font-bold mb-6 text-gradient">Quick Message</h3>
                        <form class="space-y-6">
                            <div>
                                <input type="text" placeholder="Your Name" class="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary transition-all">
                            </div>
                            <div>
                                <input type="email" placeholder="Your Email" class="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary transition-all">
                            </div>
                            <div>
                                <textarea rows="4" placeholder="Your Message" class="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary transition-all resize-none"></textarea>
                            </div>
                            <button type="submit" class="w-full bg-gradient-to-r from-primary to-secondary text-white px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105">
                                Send Message 🚀
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stacked Dialog -->
    <div id="stackedDialog" class="fixed inset-0 z-50 hidden">
        <div class="dialog-backdrop absolute inset-0" onclick="closeStackedDialog()"></div>
        <div class="absolute inset-0 flex items-center justify-center p-4">
            <div class="stacked-dialog w-full max-w-2xl">
                <div class="dialog-stack bg-white rounded-2xl p-8 text-black">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face" alt="Profile" class="w-12 h-12 rounded-full mr-4">
                            <div>
                                <h3 class="text-2xl font-bold">My Product Philosophy</h3>
                                <p class="text-gray-600">Building with purpose and passion</p>
                            </div>
                        </div>
                        <button onclick="closeStackedDialog()" class="text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <div id="dialogContent" class="mb-8">
                        <div class="dialog-slide">
                            <h4 class="text-xl font-semibold mb-4 text-primary">🎯 User-Centric Design</h4>
                            <p class="text-gray-700 mb-4">Every product decision starts with understanding the user. I believe in building solutions that don't just meet functional requirements but create delightful experiences that users love.</p>
                            <div class="bg-gray-100 p-4 rounded-lg">
                                <p class="text-sm text-gray-600">"The best products are born from deep empathy and relentless focus on solving real problems."</p>
                            </div>
                        </div>
                        
                        <div class="dialog-slide hidden">
                            <h4 class="text-xl font-semibold mb-4 text-secondary">🚀 Data-Driven Innovation</h4>
                            <p class="text-gray-700 mb-4">I combine intuition with data to make informed decisions. Every feature is validated through user research, A/B testing, and continuous iteration based on real user behavior.</p>
                            <div class="bg-gray-100 p-4 rounded-lg">
                                <p class="text-sm text-gray-600">"Innovation without validation is just expensive guesswork."</p>
                            </div>
                        </div>
                        
                        <div class="dialog-slide hidden">
                            <h4 class="text-xl font-semibold mb-4 text-accent">💡 Scalable Solutions</h4>
                            <p class="text-gray-700 mb-4">I design products with scale in mind from day one. Whether it's technical architecture or user experience, every decision considers how it will perform as we grow from hundreds to millions of users.</p>
                            <div class="bg-gray-100 p-4 rounded-lg">
                                <p class="text-sm text-gray-600">"Think big, start small, move fast - but always build to last."</p>
                            </div>
                        </div>
                        
                        <div class="dialog-slide hidden">
                            <h4 class="text-xl font-semibold mb-4 text-primary">🤝 Collaborative Leadership</h4>
                            <p class="text-gray-700 mb-4">Great products are built by great teams. I foster an environment where designers, engineers, and stakeholders work together as partners, not just service providers.</p>
                            <div class="bg-gray-100 p-4 rounded-lg">
                                <p class="text-sm text-gray-600">"The best ideas come from the intersection of different perspectives."</p>
                            </div>
                        </div>
                        
                        <div class="dialog-slide hidden">
                            <h4 class="text-xl font-semibold mb-4 text-secondary">🌟 Impact-First Mindset</h4>
                            <p class="text-gray-700 mb-4">Success isn't just about features shipped or revenue generated. It's about the positive impact we create in people's lives and the problems we solve for society.</p>
                            <div class="bg-gray-100 p-4 rounded-lg">
                                <p class="text-sm text-gray-600">"Measure success by the smiles you create, not just the metrics you improve."</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <button onclick="previousSlide()" class="flex items-center bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Previous
                        </button>
                        <div class="flex space-x-2">
                            <div class="w-2 h-2 bg-primary rounded-full slide-indicator active"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full slide-indicator"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full slide-indicator"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full slide-indicator"></div>
                            <div class="w-2 h-2 bg-gray-300 rounded-full slide-indicator"></div>
                        </div>
                        <button onclick="nextSlide()" class="flex items-center bg-primary hover:bg-primary/80 text-white px-4 py-2 rounded-lg transition-colors">
                            Next
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="py-12 bg-gray-900 border-t border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="text-2xl font-bold text-gradient mb-4">Saura Odalvi</div>
                <p class="text-gray-400 mb-6">Building the future, one product at a time</p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-primary transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-secondary transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-accent transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                        </svg>
                    </a>
                </div>
                <div class="mt-8 text-gray-500 text-sm">
                    © 2024 Saura Odalvi. All rights reserved.
                </div>
            </div>
        </div>
    </footer>

    <script>
        let currentSlide = 0;
        const totalSlides = 5;

        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }

        function openStackedDialog() {
            document.getElementById('stackedDialog').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeStackedDialog() {
            document.getElementById('stackedDialog').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                updateSlides();
            }
        }

        function previousSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                updateSlides();
            }
        }

        function updateSlides() {
            const slides = document.querySelectorAll('.dialog-slide');
            const indicators = document.querySelectorAll('.slide-indicator');
            
            slides.forEach((slide, index) => {
                slide.classList.toggle('hidden', index !== currentSlide);
            });
            
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentSlide);
                indicator.classList.toggle('bg-primary', index === currentSlide);
                indicator.classList.toggle('bg-gray-300', index !== currentSlide);
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // Close mobile menu if open
                const mobileMenu = document.getElementById('mobileMenu');
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
            });
        });

        // Animate on scroll
        function animateOnScroll() {
            const elements = document.querySelectorAll('.animate-on-scroll');
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('animate');
                }
            });
        }

        window.addEventListener('scroll', animateOnScroll);
        window.addEventListener('load', animateOnScroll);

        // Close dialog with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeStackedDialog();
            }
        });

        // Add some interactivity to project cards
        document.querySelectorAll('.card-hover').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>